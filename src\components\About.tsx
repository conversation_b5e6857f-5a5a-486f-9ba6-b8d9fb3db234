import { motion } from 'framer-motion';
import { useLanguage } from '../contexts/LanguageContext';
import { Mail, MapPin, Phone, Briefcase, GraduationCap } from 'lucide-react';

export const About = () => {
  const { t } = useLanguage();

  const experiences = [
    {
      title: 'Front-End Developer',
      company: t('freelancer'),
      period: 'June 2022 – Present',
      description: 'Built multiple dynamic websites, optimizing performance and user engagement.',
    },
    {
      title: 'Front-End Development Trainee',
      company: t('webMasters'),
      period: 'June 2025 – Sept 2025',
      description: 'Developed state-managed applications with React.js and Context API.',
    },
    {
      title: 'Software Engineer',
      company: t('faculty'),
      period: 'Aug 2021 – 2025',
      description: 'IoT projects, CPU simulation, and security systems development.',
    },
  ];

  return (
    <section id="about" className="min-h-screen py-20 px-4 relative">
      <div className="max-w-6xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <h2 className="text-5xl font-bold text-center mb-16 bg-gradient-to-r from-purple-600 to-cyan-600 dark:from-purple-400 dark:to-cyan-400 bg-clip-text text-transparent">
            {t('aboutTitle')}
          </h2>

          <div className="grid md:grid-cols-2 gap-12 mb-16">
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              viewport={{ once: true }}
              className="space-y-6"
            >
              <div className="p-6 bg-gradient-to-br from-purple-100/40 to-blue-100/40 dark:from-purple-900/30 dark:to-blue-900/30 rounded-xl backdrop-blur-sm border border-purple-300/50 dark:border-purple-500/30 hover:border-cyan-400/70 dark:hover:border-cyan-500/50 transition-all">
                <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
                  {t('aboutDescription')}
                </p>
              </div>

              <div className="space-y-4">
                <motion.div
                  whileHover={{ scale: 1.02 }}
                  className="flex items-center gap-3 p-4 bg-gradient-to-r from-purple-100/30 to-blue-100/30 dark:from-purple-900/20 dark:to-blue-900/20 rounded-lg border border-purple-300/40 dark:border-purple-500/20"
                >
                  <MapPin className="text-cyan-600 dark:text-cyan-400" size={20} />
                  <span className="text-gray-700 dark:text-gray-300">{t('location')}</span>
                </motion.div>

                <motion.div
                  whileHover={{ scale: 1.02 }}
                  className="flex items-center gap-3 p-4 bg-gradient-to-r from-purple-100/30 to-blue-100/30 dark:from-purple-900/20 dark:to-blue-900/20 rounded-lg border border-purple-300/40 dark:border-purple-500/20"
                >
                  <Phone className="text-cyan-600 dark:text-cyan-400" size={20} />
                  <span className="text-gray-700 dark:text-gray-300">{t('phone')}</span>
                </motion.div>

                <motion.div
                  whileHover={{ scale: 1.02 }}
                  className="flex items-center gap-3 p-4 bg-gradient-to-r from-purple-100/30 to-blue-100/30 dark:from-purple-900/20 dark:to-blue-900/20 rounded-lg border border-purple-300/40 dark:border-purple-500/20"
                >
                  <Mail className="text-cyan-600 dark:text-cyan-400" size={20} />
                  <a href={`mailto:${t('email')}`} className="text-gray-700 dark:text-gray-300 hover:text-cyan-600 dark:hover:text-cyan-400 transition-colors">
                    {t('email')}
                  </a>
                </motion.div>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              viewport={{ once: true }}
              className="space-y-6"
            >
              <div className="p-6 bg-gradient-to-br from-blue-100/40 to-purple-100/40 dark:from-blue-900/30 dark:to-purple-900/30 rounded-xl backdrop-blur-sm border border-blue-300/50 dark:border-blue-500/30">
                <div className="flex items-center gap-3 mb-4">
                  <Briefcase className="text-cyan-600 dark:text-cyan-400" size={24} />
                  <h3 className="text-2xl font-semibold text-purple-700 dark:text-purple-300">{t('experience')}</h3>
                </div>
                <div className="space-y-4">
                  {experiences.map((exp, index) => (
                    <motion.div
                      key={index}
                      whileHover={{ scale: 1.02 }}
                      className="p-4 bg-gradient-to-r from-purple-100/30 to-blue-100/30 dark:from-purple-900/20 dark:to-blue-900/20 rounded-lg border border-cyan-400/40 dark:border-cyan-500/20"
                    >
                      <h4 className="text-cyan-700 dark:text-cyan-300 font-semibold">{exp.title}</h4>
                      <p className="text-purple-700 dark:text-purple-300 text-sm">{exp.company}</p>
                      <p className="text-gray-600 dark:text-gray-400 text-xs mb-2">{exp.period}</p>
                      <p className="text-gray-700 dark:text-gray-300 text-sm">{exp.description}</p>
                    </motion.div>
                  ))}
                </div>
              </div>

              <div className="p-6 bg-gradient-to-br from-purple-100/40 to-blue-100/40 dark:from-purple-900/30 dark:to-blue-900/30 rounded-xl backdrop-blur-sm border border-purple-300/50 dark:border-purple-500/30">
                <div className="flex items-center gap-3 mb-4">
                  <GraduationCap className="text-cyan-600 dark:text-cyan-400" size={24} />
                  <h3 className="text-2xl font-semibold text-purple-700 dark:text-purple-300">{t('education')}</h3>
                </div>
                <motion.div
                  whileHover={{ scale: 1.02 }}
                  className="p-4 bg-gradient-to-r from-purple-100/30 to-blue-100/30 dark:from-purple-900/20 dark:to-blue-900/20 rounded-lg border border-cyan-400/40 dark:border-cyan-500/20"
                >
                  <h4 className="text-cyan-700 dark:text-cyan-300 font-semibold">Faculty of Engineering</h4>
                  <p className="text-purple-700 dark:text-purple-300 text-sm">Zagazig University</p>
                  <p className="text-gray-600 dark:text-gray-400 text-xs mb-2">BS in Computer Science (2020 – 2025)</p>
                  <p className="text-gray-700 dark:text-gray-300 text-sm">GPA: 2.7/4.0</p>
                </motion.div>
              </div>
            </motion.div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};
