import { motion } from 'framer-motion';
import { useLanguage } from '../contexts/LanguageContext';
import { useTheme } from '../contexts/ThemeContext';
import { Home, User, Sparkles, FolderOpen, Mail, Moon, Sun, Languages } from 'lucide-react';
import { useState, useEffect } from 'react';

const navItems = [
  { key: 'home', icon: Home, href: '#home' },
  { key: 'about', icon: User, href: '#about' },
  { key: 'skills', icon: Sparkles, href: '#skills' },
  { key: 'projects', icon: FolderOpen, href: '#projects' },
  { key: 'contact', icon: Mail, href: '#contact' },
];

export const Navigation = () => {
  const { t, toggleLanguage, language } = useLanguage();
  const { isDark, toggleTheme } = useTheme();
  const [activeSection, setActiveSection] = useState('home');
  const [scrolled, setScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 50);

      const sections = navItems.map((item) => item.key);
      const current = sections.find((section) => {
        const element = document.getElementById(section);
        if (element) {
          const rect = element.getBoundingClientRect();
          return rect.top <= 100 && rect.bottom >= 100;
        }
        return false;
      });
      if (current) setActiveSection(current);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const handleClick = (href: string, key: string) => {
    setActiveSection(key);
    const element = document.querySelector(href);
    element?.scrollIntoView({ behavior: 'smooth' });
  };

  return (
    <motion.nav
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      transition={{ duration: 0.5 }}
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
        scrolled ? 'bg-white/80 dark:bg-gray-900/80 backdrop-blur-md shadow-lg shadow-gray-200/20 dark:shadow-purple-900/20' : 'bg-transparent'
      }`}
    >
      <div className="max-w-7xl mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          <motion.div
            whileHover={{ scale: 1.05 }}
            className="text-2xl font-bold bg-gradient-to-r from-purple-600 to-cyan-600 dark:from-purple-400 dark:to-cyan-400 bg-clip-text text-transparent"
          >
            SA
          </motion.div>

          <div className="hidden md:flex items-center gap-2">
            {navItems.map((item, index) => {
              const Icon = item.icon;
              return (
                <motion.button
                  key={item.key}
                  onClick={() => handleClick(item.href, item.key)}
                  initial={{ opacity: 0, y: -20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  whileHover={{ scale: 1.1, rotateY: 10 }}
                  whileTap={{ scale: 0.95 }}
                  className={`relative px-4 py-2 rounded-lg transition-all ${
                    activeSection === item.key
                      ? 'bg-gradient-to-r from-purple-600 to-blue-600 shadow-lg shadow-purple-500/50'
                      : 'hover:bg-gray-200/50 dark:hover:bg-purple-900/30'
                  }`}
                  style={{ perspective: '1000px' }}
                >
                  <div className="flex items-center gap-2">
                    <Icon size={18} />
                    <span className="text-sm font-semibold">{t(item.key)}</span>
                  </div>
                  {activeSection === item.key && (
                    <motion.div
                      layoutId="activeIndicator"
                      className="absolute inset-0 border-2 border-cyan-400 rounded-lg"
                      transition={{ type: 'spring', stiffness: 300, damping: 30 }}
                    />
                  )}
                </motion.button>
              );
            })}
          </div>

          <div className="flex items-center gap-2">
            <motion.button
              onClick={toggleTheme}
              whileHover={{ scale: 1.1, rotate: 180 }}
              whileTap={{ scale: 0.95 }}
              className="p-2 rounded-lg bg-gray-200/50 dark:bg-purple-900/30 hover:bg-gray-300/50 dark:hover:bg-purple-900/50 transition-all"
            >
              {isDark ? <Sun size={20} /> : <Moon size={20} />}
            </motion.button>

            <motion.button
              onClick={toggleLanguage}
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.95 }}
              className="p-2 rounded-lg bg-gray-200/50 dark:bg-purple-900/30 hover:bg-gray-300/50 dark:hover:bg-purple-900/50 transition-all flex items-center gap-2"
            >
              <Languages size={20} />
              <span className="text-sm font-semibold hidden sm:inline">{language === 'en' ? 'AR' : 'EN'}</span>
            </motion.button>
          </div>
        </div>

        <div className="md:hidden mt-4 flex justify-center gap-2 overflow-x-auto pb-2">
          {navItems.map((item) => {
            const Icon = item.icon;
            return (
              <motion.button
                key={item.key}
                onClick={() => handleClick(item.href, item.key)}
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.95 }}
                className={`relative p-3 rounded-lg transition-all min-w-fit ${
                  activeSection === item.key
                    ? 'bg-gradient-to-r from-purple-600 to-blue-600 shadow-lg shadow-purple-500/50'
                    : 'hover:bg-gray-200/50 dark:hover:bg-purple-900/30'
                }`}
              >
                <Icon size={18} />
                {activeSection === item.key && (
                  <motion.div
                    layoutId="mobileActiveIndicator"
                    className="absolute inset-0 border-2 border-cyan-400 rounded-lg"
                    transition={{ type: 'spring', stiffness: 300, damping: 30 }}
                  />
                )}
              </motion.button>
            );
          })}
        </div>
      </div>
    </motion.nav>
  );
};
