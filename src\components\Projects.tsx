import { motion } from 'framer-motion';
import { useLanguage } from '../contexts/LanguageContext';
import { ExternalLink, Github } from 'lucide-react';

const projects = [
  {
    title: 'Solar Panel Installation Website',
    description: 'Responsive solar panel installation company website showcasing services, partnerships, and testimonials with modern green-themed design.',
    tech: ['HTML5', 'CSS3', 'Font Awesome'],
    github: 'https://github.com/SaraAber/repo',
    card: '🌞',
  },
  {
    title: 'Government Services Portal',
    description: 'Admin portal for government services featuring user management, service catalog, request tracking, analytics dashboard with charts and real-time data.',
    tech: ['React', 'MUI', 'Redux', 'Recharts', 'Axios'],
    github: 'https://github.com/SaraAber/repo',
    card: '🏛️',
  },
  {
    title: 'IoT Web Application',
    description: 'Control LED remotely and monitor temperature adaptations through web interface. Full integration of hardware and software systems.',
    tech: ['JavaScript', 'IoT', 'Web APIs'],
    github: '#',
    card: '💡',
  },
  {
    title: 'CPU Simulation',
    description: 'Software simulation to control CPU operations, demonstrating understanding of computer architecture and low-level system control.',
    tech: ['C++', 'Computer Architecture'],
    github: '#',
    card: '🖥️',
  },
];

export const Projects = () => {
  const { t } = useLanguage();

  return (
    <section id="projects" className="min-h-screen py-20 px-4 relative">
      <div className="max-w-6xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <h2 className="text-5xl font-bold text-center mb-16 bg-gradient-to-r from-purple-600 to-cyan-600 dark:from-purple-400 dark:to-cyan-400 bg-clip-text text-transparent">
            {t('projectsTitle')}
          </h2>

          <div className="grid md:grid-cols-2 gap-8">
            {projects.map((project, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 50, rotateY: -15 }}
                whileInView={{ opacity: 1, y: 0, rotateY: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{ scale: 1.05, rotateY: 5 }}
                className="relative group"
                style={{ perspective: '1000px' }}
              >
                <div className="relative h-full p-6 bg-gradient-to-br from-purple-100/50 to-blue-100/50 dark:from-purple-900/40 dark:to-blue-900/40 rounded-xl backdrop-blur-sm border border-purple-300/50 dark:border-purple-500/30 hover:border-cyan-400/70 dark:hover:border-cyan-500/50 transition-all overflow-hidden">
                  <motion.div
                    className="absolute inset-0 bg-gradient-to-br from-cyan-200/20 to-purple-200/20 dark:from-cyan-500/10 dark:to-purple-500/10"
                    initial={{ opacity: 0 }}
                    whileHover={{ opacity: 1 }}
                    transition={{ duration: 0.3 }}
                  />

                  <div className="absolute top-4 right-4 text-6xl opacity-20 group-hover:opacity-30 transition-opacity">
                    {project.card}
                  </div>

                  <div className="relative z-10">
                    <motion.div
                      className="w-16 h-16 mb-4 bg-gradient-to-br from-purple-600 to-blue-600 rounded-lg flex items-center justify-center"
                      whileHover={{ rotate: 360 }}
                      transition={{ duration: 0.6 }}
                    >
                      <span className="text-3xl">{project.card}</span>
                    </motion.div>

                    <h3 className="text-2xl font-semibold text-cyan-700 dark:text-cyan-300 mb-3">{project.title}</h3>
                    <p className="text-gray-700 dark:text-gray-300 text-sm mb-4 leading-relaxed">{project.description}</p>

                    <div className="flex flex-wrap gap-2 mb-4">
                      {project.tech.map((tech, techIndex) => (
                        <motion.span
                          key={techIndex}
                          initial={{ opacity: 0, scale: 0 }}
                          whileInView={{ opacity: 1, scale: 1 }}
                          transition={{ delay: techIndex * 0.05 }}
                          viewport={{ once: true }}
                          className="px-3 py-1 text-xs bg-gradient-to-r from-purple-300/50 to-blue-300/50 dark:from-purple-600/50 dark:to-blue-600/50 rounded-full text-cyan-700 dark:text-cyan-300 border border-cyan-400/50 dark:border-cyan-500/30"
                        >
                          {tech}
                        </motion.span>
                      ))}
                    </div>

                    <div className="flex gap-4">
                      {project.github !== '#' && (
                        <motion.a
                          href={project.github}
                          target="_blank"
                          rel="noopener noreferrer"
                          whileHover={{ scale: 1.1 }}
                          whileTap={{ scale: 0.95 }}
                          className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg text-sm hover:shadow-lg hover:shadow-purple-500/50 transition-all"
                        >
                          <Github size={16} />
                          GitHub
                        </motion.a>
                      )}
                      <motion.button
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.95 }}
                        className="flex items-center gap-2 px-4 py-2 border border-cyan-400/70 dark:border-cyan-500/50 rounded-lg text-sm hover:bg-cyan-200/20 dark:hover:bg-cyan-500/10 transition-all"
                      >
                        <ExternalLink size={16} />
                        View
                      </motion.button>
                    </div>
                  </div>

                  <motion.div
                    className="absolute -bottom-20 -right-20 w-40 h-40 bg-cyan-500/10 rounded-full blur-3xl"
                    animate={{
                      scale: [1, 1.2, 1],
                      opacity: [0.3, 0.5, 0.3],
                    }}
                    transition={{
                      duration: 3,
                      repeat: Infinity,
                      ease: 'easeInOut',
                    }}
                  />
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </section>
  );
};
