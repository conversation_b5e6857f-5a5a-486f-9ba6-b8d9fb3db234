import { motion } from 'framer-motion';
import { useLanguage } from '../contexts/LanguageContext';
import { useState } from 'react';

const skillsData = {
  languages: ['HTML5', 'CSS3', 'JavaScript (ES6+)', 'React.js', 'Bootstrap'],
  tools: ['Git/GitHub', 'Webpack', 'Babel', 'NPM', 'Chrome DevTools'],
  stateManagement: ['Redux', 'Context API'],
  performance: ['SEO', 'Debugging', 'Lazy Loading', 'Cross-Browser Compatibility'],
  design: ['Figma', 'Adobe Photoshop', 'Wireframing', 'Usability Testing'],
  softSkills: ['Communication', 'Problem-Solving', 'Team Collaboration', 'Time Management', 'Creativity'],
};

const constellationPositions = [
  { x: 20, y: 20 },
  { x: 80, y: 15 },
  { x: 60, y: 40 },
  { x: 30, y: 60 },
  { x: 75, y: 65 },
  { x: 15, y: 80 },
  { x: 50, y: 85 },
  { x: 85, y: 85 },
];

export const Skills = () => {
  const { t } = useLanguage();
  const [hoveredCategory, setHoveredCategory] = useState<string | null>(null);

  const categories = [
    { key: 'languages', skills: skillsData.languages },
    { key: 'tools', skills: skillsData.tools },
    { key: 'stateManagement', skills: skillsData.stateManagement },
    { key: 'performance', skills: skillsData.performance },
    { key: 'design', skills: skillsData.design },
    { key: 'softSkills', skills: skillsData.softSkills },
  ];

  return (
    <section id="skills" className="min-h-screen py-20 px-4 relative">
      <div className="max-w-6xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <h2 className="text-5xl font-bold text-center mb-16 bg-gradient-to-r from-cyan-600 to-purple-600 dark:from-cyan-400 dark:to-purple-400 bg-clip-text text-transparent">
            {t('skillsTitle')}
          </h2>

          <div className="relative mb-16 h-96 bg-gradient-to-br from-purple-100/30 to-blue-100/30 dark:from-purple-900/20 dark:to-blue-900/20 rounded-xl backdrop-blur-sm border border-purple-300/50 dark:border-purple-500/30 overflow-hidden">
            <svg className="absolute inset-0 w-full h-full">
              {constellationPositions.map((pos, i) => {
                if (i < constellationPositions.length - 1) {
                  const nextPos = constellationPositions[i + 1];
                  return (
                    <motion.line
                      key={`line-${i}`}
                      x1={`${pos.x}%`}
                      y1={`${pos.y}%`}
                      x2={`${nextPos.x}%`}
                      y2={`${nextPos.y}%`}
                      stroke="rgba(34, 211, 238, 0.3)"
                      strokeWidth="2"
                      initial={{ pathLength: 0 }}
                      whileInView={{ pathLength: 1 }}
                      transition={{ duration: 1.5, delay: i * 0.2 }}
                      viewport={{ once: true }}
                    />
                  );
                }
                return null;
              })}

              {constellationPositions.map((pos, i) => (
                <motion.circle
                  key={`circle-${i}`}
                  cx={`${pos.x}%`}
                  cy={`${pos.y}%`}
                  r="8"
                  fill="rgba(34, 211, 238, 0.8)"
                  initial={{ scale: 0, opacity: 0 }}
                  whileInView={{ scale: 1, opacity: 1 }}
                  transition={{ duration: 0.5, delay: i * 0.1 }}
                  viewport={{ once: true }}
                  animate={{
                    boxShadow: [
                      '0 0 0px rgba(34, 211, 238, 0.8)',
                      '0 0 20px rgba(34, 211, 238, 0.8)',
                      '0 0 0px rgba(34, 211, 238, 0.8)',
                    ],
                    scale: [1, 1.2, 1],
                  }}
                  style={{
                    filter: 'drop-shadow(0 0 10px rgba(34, 211, 238, 0.8))',
                  }}
                />
              ))}
            </svg>

            {constellationPositions.slice(0, 6).map((pos, i) => (
              <motion.div
                key={`label-${i}`}
                className="absolute text-xs text-cyan-700 dark:text-cyan-300 font-semibold"
                style={{
                  left: `${pos.x}%`,
                  top: `${pos.y}%`,
                  transform: 'translate(-50%, -150%)',
                }}
                initial={{ opacity: 0 }}
                whileInView={{ opacity: 1 }}
                transition={{ delay: i * 0.2 + 0.5 }}
                viewport={{ once: true }}
              >
                {t(categories[i]?.key)}
              </motion.div>
            ))}
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {categories.map((category, index) => (
              <motion.div
                key={category.key}
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                onHoverStart={() => setHoveredCategory(category.key)}
                onHoverEnd={() => setHoveredCategory(null)}
                className="p-6 bg-gradient-to-br from-purple-100/40 to-blue-100/40 dark:from-purple-900/30 dark:to-blue-900/30 rounded-xl backdrop-blur-sm border border-purple-300/50 dark:border-purple-500/30 hover:border-cyan-400/70 dark:hover:border-cyan-500/50 transition-all"
              >
                <motion.h3
                  className="text-xl font-semibold mb-4 bg-gradient-to-r from-cyan-600 to-purple-600 dark:from-cyan-400 dark:to-purple-400 bg-clip-text text-transparent"
                  animate={{
                    scale: hoveredCategory === category.key ? 1.05 : 1,
                  }}
                >
                  {t(category.key)}
                </motion.h3>
                <ul className="space-y-2">
                  {category.skills.map((skill, skillIndex) => (
                    <motion.li
                      key={skillIndex}
                      initial={{ opacity: 0, x: -20 }}
                      whileInView={{ opacity: 1, x: 0 }}
                      transition={{ delay: skillIndex * 0.05 }}
                      viewport={{ once: true }}
                      className="text-gray-700 dark:text-gray-300 text-sm flex items-center gap-2"
                    >
                      <motion.span
                        className="w-2 h-2 bg-cyan-500 dark:bg-cyan-400 rounded-full"
                        animate={{
                          scale: hoveredCategory === category.key ? [1, 1.5, 1] : 1,
                          boxShadow:
                            hoveredCategory === category.key
                              ? [
                                  '0 0 0px rgba(34, 211, 238, 0.8)',
                                  '0 0 10px rgba(34, 211, 238, 0.8)',
                                  '0 0 0px rgba(34, 211, 238, 0.8)',
                                ]
                              : '0 0 0px rgba(34, 211, 238, 0.8)',
                        }}
                        transition={{ duration: 1, repeat: hoveredCategory === category.key ? Infinity : 0 }}
                      />
                      {skill}
                    </motion.li>
                  ))}
                </ul>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </section>
  );
};
