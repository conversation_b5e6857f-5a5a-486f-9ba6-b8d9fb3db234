import { motion } from 'framer-motion';
import { useLanguage } from '../contexts/LanguageContext';
import { Send, Mail, Phone, MapPin } from 'lucide-react';
import { useState } from 'react';

export const Contact = () => {
  const { t } = useLanguage();
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    message: '',
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const mailtoLink = `mailto:${t('email')}?subject=Message from ${formData.name}&body=${formData.message}`;
    window.location.href = mailtoLink;
  };

  return (
    <section id="contact" className="min-h-screen py-20 px-4 relative">
      <div className="max-w-4xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <h2 className="text-5xl font-bold text-center mb-16 bg-gradient-to-r from-cyan-600 to-purple-600 dark:from-cyan-400 dark:to-purple-400 bg-clip-text text-transparent">
            {t('contactTitle')}
          </h2>

          <div className="grid md:grid-cols-2 gap-8">
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              viewport={{ once: true }}
              className="space-y-6"
            >
              <div className="p-6 bg-gradient-to-br from-purple-100/40 to-blue-100/40 dark:from-purple-900/30 dark:to-blue-900/30 rounded-xl backdrop-blur-sm border border-purple-300/50 dark:border-purple-500/30">
                <h3 className="text-2xl font-semibold text-cyan-700 dark:text-cyan-300 mb-6">Get in Touch</h3>
                <div className="space-y-4">
                  <motion.a
                    href={`mailto:${t('email')}`}
                    whileHover={{ scale: 1.02, x: 10 }}
                    className="flex items-center gap-4 p-4 bg-gradient-to-r from-purple-100/30 to-blue-100/30 dark:from-purple-900/20 dark:to-blue-900/20 rounded-lg border border-cyan-400/40 dark:border-cyan-500/20 hover:border-cyan-500/70 dark:hover:border-cyan-500/50 transition-all"
                  >
                    <div className="w-12 h-12 bg-gradient-to-br from-purple-600 to-blue-600 rounded-lg flex items-center justify-center">
                      <Mail className="text-cyan-200 dark:text-cyan-300" size={20} />
                    </div>
                    <div>
                      <p className="text-gray-600 dark:text-gray-400 text-sm">Email</p>
                      <p className="text-cyan-700 dark:text-cyan-300">{t('email')}</p>
                    </div>
                  </motion.a>

                  <motion.a
                    href={`tel:${t('phone')}`}
                    whileHover={{ scale: 1.02, x: 10 }}
                    className="flex items-center gap-4 p-4 bg-gradient-to-r from-purple-100/30 to-blue-100/30 dark:from-purple-900/20 dark:to-blue-900/20 rounded-lg border border-cyan-400/40 dark:border-cyan-500/20 hover:border-cyan-500/70 dark:hover:border-cyan-500/50 transition-all"
                  >
                    <div className="w-12 h-12 bg-gradient-to-br from-purple-600 to-blue-600 rounded-lg flex items-center justify-center">
                      <Phone className="text-cyan-200 dark:text-cyan-300" size={20} />
                    </div>
                    <div>
                      <p className="text-gray-600 dark:text-gray-400 text-sm">Phone</p>
                      <p className="text-cyan-700 dark:text-cyan-300">{t('phone')}</p>
                    </div>
                  </motion.a>

                  <motion.div
                    whileHover={{ scale: 1.02, x: 10 }}
                    className="flex items-center gap-4 p-4 bg-gradient-to-r from-purple-100/30 to-blue-100/30 dark:from-purple-900/20 dark:to-blue-900/20 rounded-lg border border-cyan-400/40 dark:border-cyan-500/20"
                  >
                    <div className="w-12 h-12 bg-gradient-to-br from-purple-600 to-blue-600 rounded-lg flex items-center justify-center">
                      <MapPin className="text-cyan-200 dark:text-cyan-300" size={20} />
                    </div>
                    <div>
                      <p className="text-gray-600 dark:text-gray-400 text-sm">Location</p>
                      <p className="text-cyan-700 dark:text-cyan-300">{t('location')}</p>
                    </div>
                  </motion.div>
                </div>
              </div>

              <motion.div
                className="p-6 bg-gradient-to-br from-blue-100/30 to-purple-100/30 dark:from-blue-900/20 dark:to-purple-900/20 rounded-xl border border-blue-300/40 dark:border-blue-500/20"
                animate={{
                  boxShadow: [
                    '0 0 20px rgba(139, 92, 246, 0.2)',
                    '0 0 40px rgba(59, 130, 246, 0.3)',
                    '0 0 20px rgba(139, 92, 246, 0.2)',
                  ],
                }}
                transition={{
                  duration: 3,
                  repeat: Infinity,
                  ease: 'easeInOut',
                }}
              >
                <p className="text-cyan-700 dark:text-cyan-300 text-center italic">
                  "The stars align for those who seek connection"
                </p>
              </motion.div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              viewport={{ once: true }}
            >
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="p-6 bg-gradient-to-br from-purple-100/40 to-blue-100/40 dark:from-purple-900/30 dark:to-blue-900/30 rounded-xl backdrop-blur-sm border border-purple-300/50 dark:border-purple-500/30">
                  <div className="space-y-4">
                    <div>
                      <label className="block text-cyan-700 dark:text-cyan-300 mb-2 text-sm">{t('contactName')}</label>
                      <motion.input
                        whileFocus={{ scale: 1.02 }}
                        type="text"
                        required
                        value={formData.name}
                        onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                        className="w-full px-4 py-3 bg-purple-100/30 dark:bg-purple-900/20 border border-cyan-400/50 dark:border-cyan-500/30 rounded-lg focus:border-cyan-600 dark:focus:border-cyan-500 focus:outline-none text-gray-800 dark:text-gray-300 placeholder-gray-600 dark:placeholder-gray-500"
                        placeholder={t('contactName')}
                      />
                    </div>

                    <div>
                      <label className="block text-cyan-700 dark:text-cyan-300 mb-2 text-sm">{t('contactEmail')}</label>
                      <motion.input
                        whileFocus={{ scale: 1.02 }}
                        type="email"
                        required
                        value={formData.email}
                        onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                        className="w-full px-4 py-3 bg-purple-100/30 dark:bg-purple-900/20 border border-cyan-400/50 dark:border-cyan-500/30 rounded-lg focus:border-cyan-600 dark:focus:border-cyan-500 focus:outline-none text-gray-800 dark:text-gray-300 placeholder-gray-600 dark:placeholder-gray-500"
                        placeholder={t('contactEmail')}
                      />
                    </div>

                    <div>
                      <label className="block text-cyan-700 dark:text-cyan-300 mb-2 text-sm">{t('contactMessage')}</label>
                      <motion.textarea
                        whileFocus={{ scale: 1.02 }}
                        required
                        value={formData.message}
                        onChange={(e) => setFormData({ ...formData, message: e.target.value })}
                        rows={5}
                        className="w-full px-4 py-3 bg-purple-100/30 dark:bg-purple-900/20 border border-cyan-400/50 dark:border-cyan-500/30 rounded-lg focus:border-cyan-600 dark:focus:border-cyan-500 focus:outline-none text-gray-800 dark:text-gray-300 placeholder-gray-600 dark:placeholder-gray-500 resize-none"
                        placeholder={t('contactMessage')}
                      />
                    </div>

                    <motion.button
                      type="submit"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      className="w-full py-3 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg font-semibold flex items-center justify-center gap-2 hover:shadow-lg hover:shadow-purple-500/50 transition-all"
                    >
                      <Send size={20} />
                      {t('contactSend')}
                    </motion.button>
                  </div>
                </div>
              </form>
            </motion.div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};
