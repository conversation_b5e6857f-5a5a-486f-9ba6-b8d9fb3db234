import { motion } from 'framer-motion';
import { useLanguage } from '../contexts/LanguageContext';
import { useState, useEffect } from 'react';

const fortuneMessages = {
  en: [
    "Your code will compile on the first try today",
    "A brilliant solution awaits in the next component",
    "Success follows those who embrace innovation",
    "Your creativity knows no bounds",
    "Great opportunities are on the horizon",
  ],
  ar: [
    "سيتم تجميع الكود الخاص بك في المحاولة الأولى اليوم",
    "حل رائع ينتظرك في المكون التالي",
    "النجاح يتبع أولئك الذين يتبنون الابتكار",
    "إبداعك لا يعرف حدودًا",
    "فرص عظيمة في الأفق",
  ],
};

export const Hero = () => {
  const { t, language } = useLanguage();
  const [fortune, setFortune] = useState(0);
  const [isHovered, setIsHovered] = useState(false);

  useEffect(() => {
    const interval = setInterval(() => {
      setFortune((prev) => (prev + 1) % fortuneMessages[language].length);
    }, 5000);
    return () => clearInterval(interval);
  }, [language]);

  return (
    <section id="home" className="min-h-screen flex items-center justify-center relative overflow-hidden">
      <motion.div
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 1 }}
        className="text-center z-10 px-4"
      >
        <motion.div
          className="relative mx-auto mb-8"
          style={{ width: '300px', height: '300px' }}
          onHoverStart={() => setIsHovered(true)}
          onHoverEnd={() => setIsHovered(false)}
          animate={{
            scale: isHovered ? 1.1 : 1,
          }}
          transition={{ duration: 0.3 }}
        >
          <motion.div
            className="absolute inset-0 rounded-full"
            style={{
              background: 'radial-gradient(circle, rgba(139, 92, 246, 0.3), rgba(59, 130, 246, 0.2), transparent)',
              filter: 'blur(30px)',
            }}
            animate={{
              scale: [1, 1.2, 1],
              opacity: [0.5, 0.8, 0.5],
            }}
            transition={{
              duration: 3,
              repeat: Infinity,
              ease: 'easeInOut',
            }}
          />

          <motion.div
            className="absolute inset-0 rounded-full border-4 border-cyan-400/30"
            animate={{
              rotate: 360,
            }}
            transition={{
              duration: 20,
              repeat: Infinity,
              ease: 'linear',
            }}
            style={{
              boxShadow: '0 0 60px rgba(34, 211, 238, 0.4)',
            }}
          />

          <motion.div
            className="absolute inset-4 rounded-full bg-gradient-to-br from-purple-600/40 via-blue-600/40 to-cyan-500/40 backdrop-blur-sm flex items-center justify-center"
            animate={{
              boxShadow: [
                '0 0 40px rgba(139, 92, 246, 0.6)',
                '0 0 80px rgba(59, 130, 246, 0.8)',
                '0 0 40px rgba(139, 92, 246, 0.6)',
              ],
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: 'easeInOut',
            }}
          >
            <div className="text-6xl">🔮</div>
          </motion.div>

          {[...Array(12)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-2 h-2 bg-cyan-400 rounded-full"
              style={{
                top: '50%',
                left: '50%',
              }}
              animate={{
                x: [0, Math.cos((i * 30 * Math.PI) / 180) * 180],
                y: [0, Math.sin((i * 30 * Math.PI) / 180) * 180],
                opacity: [0, 1, 0],
                scale: [0, 1, 0],
              }}
              transition={{
                duration: 3,
                repeat: Infinity,
                delay: i * 0.2,
                ease: 'easeOut',
              }}
            />
          ))}
        </motion.div>

        <motion.h1
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="text-5xl md:text-7xl font-bold bg-gradient-to-r from-purple-600 via-blue-600 to-cyan-600 dark:from-purple-400 dark:via-blue-400 dark:to-cyan-400 bg-clip-text text-transparent mb-4"
        >
          {t('heroTitle')}
        </motion.h1>

        <motion.p
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.7 }}
          className="text-xl md:text-2xl text-blue-600 dark:text-blue-300 mb-8"
        >
          {t('heroSubtitle')}
        </motion.p>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.9 }}
          className="max-w-md mx-auto p-4 bg-gradient-to-r from-purple-100/50 to-blue-100/50 dark:from-purple-900/30 dark:to-blue-900/30 rounded-lg backdrop-blur-sm border border-cyan-300/50 dark:border-cyan-500/30"
        >
          <motion.p
            key={fortune}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="text-cyan-700 dark:text-cyan-300 italic"
          >
            "{fortuneMessages[language][fortune]}"
          </motion.p>
        </motion.div>

        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 1.2 }}
          className="mt-8 flex justify-center gap-4"
        >
          <a
            href={`mailto:${t('email')}`}
            className="px-6 py-3 bg-gradient-to-r from-purple-600 to-blue-600 rounded-full hover:shadow-lg hover:shadow-purple-500/50 transition-all"
          >
            {t('contact')}
          </a>
        </motion.div>
      </motion.div>
    </section>
  );
};
