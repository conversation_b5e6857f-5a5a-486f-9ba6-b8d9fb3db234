import { createContext, useContext, useState, ReactNode } from 'react';

type Language = 'en' | 'ar';

interface LanguageContextType {
  language: Language;
  toggleLanguage: () => void;
  t: (key: string) => string;
}

const translations = {
  en: {
    home: 'Home',
    about: 'About',
    skills: 'Skills',
    projects: 'Projects',
    contact: 'Contact',
    heroTitle: '<PERSON>',
    heroSubtitle: 'Front-End Developer — ReactJS',
    heroQuote: 'Discover your digital destiny',
    aboutTitle: 'About Me',
    aboutDescription: 'Dynamic and results-oriented Front-End Developer with 2+ years of experience, specializing in HTML, CSS, JavaScript, React.js, Bootstrap and Git. Adept at responsive design, UI/UX best practices, performance optimization, and web accessibility. Known for a strong problem solving mindset, adaptability, and the ability to collaborate effectively within cross-functional teams.',
    skillsTitle: 'Skills Constellation',
    projectsTitle: 'Mystical Projects',
    contactTitle: 'Reach Out',
    contactName: 'Your Name',
    contactEmail: 'Your Email',
    contactMessage: 'Your Message',
    contactSend: 'Send Message',
    location: 'Belbise Al sharqia',
    phone: '01026611556',
    email: '<EMAIL>',
    experience: 'Experience',
    education: 'Education',
    freelancer: 'Freelancer',
    webMasters: 'Web Masters',
    faculty: 'Faculty of Engineering',
    languages: 'Languages & Frameworks',
    tools: 'Development Tools',
    stateManagement: 'State Management',
    performance: 'Performance & Optimization',
    design: 'Design & UI/UX',
    softSkills: 'Soft Skills',
  },
  ar: {
    home: 'الرئيسية',
    about: 'نبذة',
    skills: 'المهارات',
    projects: 'المشاريع',
    contact: 'اتصل',
    heroTitle: 'سارة عابر',
    heroSubtitle: 'مطورة واجهات أمامية — ReactJS',
    heroQuote: 'اكتشف مصيرك الرقمي',
    aboutTitle: 'نبذة عني',
    aboutDescription: 'مطورة واجهات أمامية ديناميكية وموجهة نحو النتائج مع أكثر من عامين من الخبرة، متخصصة في HTML و CSS و JavaScript و React.js و Bootstrap و Git. بارعة في التصميم المتجاوب وأفضل ممارسات واجهة المستخدم وتجربة المستخدم وتحسين الأداء وإمكانية الوصول إلى الويب. معروفة بعقلية قوية في حل المشكلات والقدرة على التكيف والقدرة على التعاون بفعالية ضمن فرق متعددة الوظائف.',
    skillsTitle: 'كوكبة المهارات',
    projectsTitle: 'المشاريع الغامضة',
    contactTitle: 'تواصل معي',
    contactName: 'اسمك',
    contactEmail: 'بريدك الإلكتروني',
    contactMessage: 'رسالتك',
    contactSend: 'إرسال الرسالة',
    location: 'بلبيس الشرقية',
    phone: '01026611556',
    email: '<EMAIL>',
    experience: 'الخبرة',
    education: 'التعليم',
    freelancer: 'عمل حر',
    webMasters: 'ويب ماسترز',
    faculty: 'كلية الهندسة',
    languages: 'اللغات والأطر',
    tools: 'أدوات التطوير',
    stateManagement: 'إدارة الحالة',
    performance: 'الأداء والتحسين',
    design: 'التصميم وواجهة المستخدم',
    softSkills: 'المهارات الناعمة',
  },
};

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

export const LanguageProvider = ({ children }: { children: ReactNode }) => {
  const [language, setLanguage] = useState<Language>('en');

  const toggleLanguage = () => {
    setLanguage((prev) => (prev === 'en' ? 'ar' : 'en'));
  };

  const t = (key: string): string => {
    return translations[language][key as keyof typeof translations.en] || key;
  };

  return (
    <LanguageContext.Provider value={{ language, toggleLanguage, t }}>
      <div dir={language === 'ar' ? 'rtl' : 'ltr'} className={language === 'ar' ? 'font-arabic' : ''}>
        {children}
      </div>
    </LanguageContext.Provider>
  );
};

export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (!context) {
    throw new Error('useLanguage must be used within LanguageProvider');
  }
  return context;
};
